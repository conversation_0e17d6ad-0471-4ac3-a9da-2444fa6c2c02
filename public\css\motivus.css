/* Motivus Custom Styles - Mobile First Design */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    line-height: 1.6;
    color: #333;
}

/* Mobile Container */
.mobile-container {
    max-width: 375px;
    margin: 0 auto;
    min-height: 100vh;
    background: #fff;
    position: relative;
    box-shadow: 0 0 20px rgba(0,0,0,0.1);
}

/* Homepage Styles */
.homepage {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
    color: white;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 40px 30px 60px;
    position: relative;
    overflow: hidden;
}

.homepage::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    pointer-events: none;
}

.logo-section {
    text-align: center;
    z-index: 2;
    position: relative;
}

.logo {
    font-size: 2.5rem;
    font-weight: 700;
    letter-spacing: 3px;
    margin-bottom: 10px;
    background: linear-gradient(45deg, #00d4ff, #0099cc);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.tagline {
    font-size: 0.9rem;
    opacity: 0.8;
    font-weight: 300;
}

.car-section {
    text-align: center;
    z-index: 2;
    position: relative;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.car-image {
    width: 280px;
    height: 160px;
    background: linear-gradient(45deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05));
    border-radius: 20px;
    margin: 20px 0;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.1);
    position: relative;
    overflow: hidden;
}

.car-image::before {
    content: '🚗';
    font-size: 4rem;
    opacity: 0.7;
}

.car-text {
    margin-top: 30px;
    text-align: center;
}

.car-text h2 {
    font-size: 1.4rem;
    font-weight: 600;
    margin-bottom: 8px;
    line-height: 1.3;
}

.car-text p {
    font-size: 0.9rem;
    opacity: 0.8;
    font-weight: 300;
}

.cta-section {
    z-index: 2;
    position: relative;
}

.get-started-btn {
    width: 100%;
    background: linear-gradient(45deg, #00d4ff, #0099cc);
    color: white;
    border: none;
    padding: 18px;
    border-radius: 25px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: block;
    text-align: center;
    box-shadow: 0 8px 25px rgba(0, 212, 255, 0.3);
}

.get-started-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 35px rgba(0, 212, 255, 0.4);
    color: white;
    text-decoration: none;
}

/* Auth Pages Styles */
.auth-container {
    background: #f8f9fa;
    min-height: 100vh;
    padding: 20px;
}

.auth-card {
    background: white;
    border-radius: 20px;
    padding: 40px 30px;
    box-shadow: 0 10px 40px rgba(0,0,0,0.1);
    margin-top: 60px;
}

.auth-header {
    text-align: center;
    margin-bottom: 40px;
}

.auth-logo {
    font-size: 2rem;
    font-weight: 700;
    color: #0099cc;
    margin-bottom: 10px;
}

.auth-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
}

.auth-subtitle {
    color: #666;
    font-size: 0.9rem;
}

.form-group {
    margin-bottom: 20px;
}

.form-label {
    display: block;
    margin-bottom: 8px;
    color: #333;
    font-weight: 500;
    font-size: 0.9rem;
}

.form-input {
    width: 100%;
    padding: 15px;
    border: 2px solid #e1e5e9;
    border-radius: 12px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: #fff;
}

.form-input:focus {
    outline: none;
    border-color: #0099cc;
    box-shadow: 0 0 0 3px rgba(0, 153, 204, 0.1);
}

.form-input::placeholder {
    color: #999;
}

/* Role Selection */
.role-selection {
    margin: 25px 0;
}

.role-options {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
    margin-top: 15px;
}

.role-option {
    position: relative;
}

.role-option input[type="radio"] {
    position: absolute;
    opacity: 0;
    width: 100%;
    height: 100%;
    margin: 0;
    cursor: pointer;
}

.role-option label {
    display: block;
    padding: 15px 10px;
    border: 2px solid #e1e5e9;
    border-radius: 12px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    font-weight: 500;
    background: #fff;
}

.role-option input[type="radio"]:checked + label {
    border-color: #0099cc;
    background: #f0f9ff;
    color: #0099cc;
}

.primary-btn {
    width: 100%;
    background: linear-gradient(45deg, #00d4ff, #0099cc);
    color: white;
    border: none;
    padding: 18px;
    border-radius: 25px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin: 20px 0;
}

.primary-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 8px 25px rgba(0, 212, 255, 0.3);
}

.divider {
    text-align: center;
    margin: 25px 0;
    position: relative;
    color: #999;
    font-size: 0.9rem;
}

.divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #e1e5e9;
    z-index: 1;
}

.divider span {
    background: white;
    padding: 0 15px;
    position: relative;
    z-index: 2;
}

.google-btn {
    width: 100%;
    background: white;
    color: #333;
    border: 2px solid #e1e5e9;
    padding: 15px;
    border-radius: 12px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    text-decoration: none;
}

.google-btn:hover {
    border-color: #ccc;
    background: #f8f9fa;
    color: #333;
    text-decoration: none;
}

.auth-footer {
    text-align: center;
    margin-top: 30px;
    color: #666;
    font-size: 0.9rem;
}

.auth-footer a {
    color: #0099cc;
    text-decoration: none;
    font-weight: 500;
}

.auth-footer a:hover {
    text-decoration: underline;
}

/* Verification Page Styles */
.verification-container {
    text-align: center;
    padding: 60px 30px;
}

.verification-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 10px;
    color: #333;
}

.verification-subtitle {
    color: #666;
    margin-bottom: 40px;
    font-size: 0.9rem;
    line-height: 1.5;
}

.otp-container {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin: 40px 0;
}

.otp-input {
    width: 50px;
    height: 50px;
    border: 2px solid #e1e5e9;
    border-radius: 12px;
    text-align: center;
    font-size: 1.5rem;
    font-weight: 600;
    color: #333;
    transition: all 0.3s ease;
}

.otp-input:focus {
    outline: none;
    border-color: #0099cc;
    box-shadow: 0 0 0 3px rgba(0, 153, 204, 0.1);
}

.resend-text {
    color: #666;
    font-size: 0.9rem;
    margin-top: 30px;
}

.resend-link {
    color: #0099cc;
    text-decoration: none;
    font-weight: 500;
}

.resend-link:hover {
    text-decoration: underline;
}

/* Alert Styles */
.alert {
    padding: 15px;
    border-radius: 12px;
    margin-bottom: 20px;
    font-size: 0.9rem;
}

.alert-danger {
    background: #fef2f2;
    border: 1px solid #fecaca;
    color: #dc2626;
}

.alert-success {
    background: #f0fdf4;
    border: 1px solid #bbf7d0;
    color: #16a34a;
}

/* Responsive Design */
@media (max-width: 480px) {
    .mobile-container {
        max-width: 100%;
    }
    
    .homepage {
        padding: 30px 20px 50px;
    }
    
    .car-image {
        width: 250px;
        height: 140px;
    }
    
    .auth-card {
        padding: 30px 20px;
        margin-top: 40px;
    }
    
    .otp-input {
        width: 45px;
        height: 45px;
        font-size: 1.3rem;
    }
    
    .otp-container {
        gap: 10px;
    }
}
